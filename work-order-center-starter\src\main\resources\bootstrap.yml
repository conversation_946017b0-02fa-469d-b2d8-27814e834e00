server:
  port: 9099
  tomcat:
    max-threads: 300
    min-spare-threads: 30
    accept-count: 200
spring:
  profiles:
    active: production
  application:
    name: work-order-center
  resources:
    static-locations: classpath:/static/,classpath:/static/doc/
  cloud:
    nacos:
      # dataId 的完整格式为:${prefix}-${spring.profiles.active}.${file-extension}
      config:
        # nacos服务地址
        server-addr: ${nacos.server.addr}
        # 空间隔离,默认public
        namespace: ${nacos.namespace}
        # 公共配置文件
        shared-configs:
          - dataId: common-work-order-${spring.profiles.active}.yaml
            group: ${nacos.config.group}
        # 常规配置文件
        extension-configs:
          - dataId: ${spring.application.name}.yaml
            group: ${nacos.config.group}
            # 开启动态刷新
            refresh: true
      username: ${nacos.username}
      password: ${nacos.password}
      # 服务注册
      discovery:
        # 关闭服务注册,除非需要使用http://微服务名称调用API
        enabled: false
        # nacos宕机后,是否从本地文件中加载配置文件及注册的服务
        namingLoadCacheAtStart: true
  batch:
    initialize-schema: always
    job:
      enabled: false



#http:
#  vgpuDeviceMetricUrl: http://188.105.225.248:30090
#  gpuDeviceResourceUrl: http://188.105.225.248:30123
