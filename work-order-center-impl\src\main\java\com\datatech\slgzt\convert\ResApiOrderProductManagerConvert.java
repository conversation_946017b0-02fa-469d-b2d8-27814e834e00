package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.ResApiOrderProductDO;
import com.datatech.slgzt.model.dto.CorporateOrderProductDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ResApiOrderProductManagerConvert {

    CorporateOrderProductDTO do2dto(ResApiOrderProductDO corporateOrderProduct);

    ResApiOrderProductDO dto2do(CorporateOrderProductDTO corporateOrderProductDTO);

    List<CorporateOrderProductDTO> dos2DTOs(List<ResApiOrderProductDO> productDOS);

    List<ResApiOrderProductDO> dtoList2DOs(List<CorporateOrderProductDTO> productDTOS);
} 