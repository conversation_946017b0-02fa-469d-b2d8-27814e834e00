package com.datatech.slgzt.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datatech.slgzt.dao.model.DeviceGpuInfoDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface DeviceGpuInfoMapper extends BaseMapper<DeviceGpuInfoDO> {


    @Select("SELECT DISTINCT model_name FROM WOC_DEVICE_PHYSICAL")
    List<String> groupModelName();

    @Select("<script>" +
            "SELECT DISTINCT DEPT_NAME FROM WOC_DEVICE_PHYSICAL WHERE DEPT_NAME IS NOT NULL AND MODEL_NAME = #{modelName} " +
            "<if test=\"modelName == '910B' and subModelName != null and subModelName != '' \"> " +
            "AND SUB_MODEL_NAME = #{subModelName} " +
            "</if>" +
            "</script>")
    List<String> groupDeptName(@Param("modelName") String modelName, @Param("subModelName") String subModelName);

    @Select("<script>" +
            "SELECT DISTINCT BUSINESS_SYSTEM_NAME FROM WOC_DEVICE_PHYSICAL WHERE BUSINESS_SYSTEM_NAME IS NOT NULL AND MODEL_NAME = #{modelName} " +
            "<if test=\"modelName == '910B' and subModelName != null and subModelName != '' \"> " +
            "AND SUB_MODEL_NAME = #{subModelName} " +
            "</if>" +
            "</script>")
    List<String> groupBusinessSystemName(@Param("modelName") String modelName, @Param("subModelName") String subModelName);
}
